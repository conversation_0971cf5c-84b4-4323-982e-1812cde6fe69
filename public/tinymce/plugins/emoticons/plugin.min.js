!function(){"use strict";var t=tinymce.util.Tools.resolve("tinymce.PluginManager");const e=t=>e=>t===e,o=e(null),n=e(void 0),r=t=>"function"==typeof t;const s=()=>{},a=()=>!1;class i{constructor(t,e){this.tag=t,this.value=e}static some(t){return new i(!0,t)}static none(){return i.singletonNone}fold(t,e){return this.tag?e(this.value):t()}isSome(){return this.tag}isNone(){return!this.tag}map(t){return this.tag?i.some(t(this.value)):i.none()}bind(t){return this.tag?t(this.value):i.none()}exists(t){return this.tag&&t(this.value)}forall(t){return!this.tag||t(this.value)}filter(t){return!this.tag||t(this.value)?this:i.none()}getOr(t){return this.tag?this.value:t}or(t){return this.tag?this:t}getOrThunk(t){return this.tag?this.value:t()}orThunk(t){return this.tag?this:t()}getOrDie(t){if(this.tag)return this.value;throw new Error(null!=t?t:"Called getOrDie on None")}static from(t){return null==t?i.none():i.some(t)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(t){this.tag&&t(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}i.singletonNone=new i(!1),Array.prototype.slice;const l=(t,e)=>{const o=t.length,n=new Array(o);for(let r=0;r<o;r++){const o=t[r];n[r]=e(o,r)}return n};r(Array.from)&&Array.from;const c=Object.keys,u=Object.hasOwnProperty,g=(t,e)=>{const o=c(t);for(let n=0,r=o.length;n<r;n++){const r=o[n];e(t[r],r)}},m=(t,e)=>u.call(t,e),d=t=>{let e=t;return{get:()=>e,set:t=>{e=t}}},h=(p=(t,e)=>e,(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const e={};for(let o=0;o<t.length;o++){const n=t[o];for(const t in n)m(n,t)&&(e[t]=p(e[t],n[t]))}return e});var p;const y=()=>{const t=(t=>{const e=d(i.none()),o=()=>e.get().each(t);return{clear:()=>{o(),e.set(i.none())},isSet:()=>e.get().isSome(),get:()=>e.get(),set:t=>{o(),e.set(i.some(t))}}})(s);return{...t,on:e=>t.get().each(e)}},f=(t,e,o=0,r)=>{const s=t.indexOf(e,o);return-1!==s&&(!!n(r)||s+e.length<=r)};var v=tinymce.util.Tools.resolve("tinymce.Resource");const b=t=>e=>e.options.get(t),w=b("emoticons_database"),j=b("emoticons_database_url"),C=b("emoticons_database_id"),_=b("emoticons_append"),A=b("emoticons_images_url"),k="All",O={symbols:"Symbols",people:"People",animals_and_nature:"Animals and Nature",food_and_drink:"Food and Drink",activity:"Activity",travel_and_places:"Travel and Places",objects:"Objects",flags:"Flags",user:"User Defined"},x=(t,e)=>m(t,e)?t[e]:e,E=t=>{const e=_(t);return o=t=>({keywords:[],category:"user",...t}),((t,e)=>{const o={};return g(t,((t,n)=>{const r=e(t,n);o[r.k]=r.v})),o})(e,((t,e)=>({k:e,v:o(t)})));var o},L=(t,e)=>f(t.title.toLowerCase(),e)||(t=>{for(let n=0,r=t.length;n<r;n++)if(o=t[n],f(o.toLowerCase(),e))return!0;var o;return!1})(t.keywords),S=(t,e,o)=>{const n=[],r=e.toLowerCase(),s=o.fold((()=>a),(t=>e=>e>=t));for(let o=0;o<t.length&&(0!==e.length&&!L(t[o],r)||(n.push({value:t[o].char,text:t[o].title,icon:t[o].char}),!s(n.length)));o++);return n},N="pattern",T=(t,e)=>{const n={pattern:"",results:S(e.listAll(),"",i.some(300))},r=d(k),s=(t=>{let e=null;const n=()=>{o(e)||(clearTimeout(e),e=null)};return{cancel:n,throttle:(...o)=>{n(),e=setTimeout((()=>{e=null,t.apply(null,o)}),200)}}})((t=>{(t=>{const o=t.getData(),n=r.get(),s=e.listCategory(n),a=S(s,o[N],n===k?i.some(300):i.none());t.setData({results:a})})(t)})),a={label:"Search",type:"input",name:N},c={type:"collection",name:"results"},u=()=>({title:"Emojis",size:"normal",body:{type:"tabpanel",tabs:l(e.listCategories(),(t=>({title:t,name:t,items:[a,c]})))},initialData:n,onTabChange:(t,e)=>{r.set(e.newTabName),s.throttle(t)},onChange:s.throttle,onAction:(e,o)=>{"results"===o.name&&(((t,e)=>{t.insertContent(e)})(t,o.value),e.close())},buttons:[{type:"cancel",text:"Close",primary:!0}]}),g=t.windowManager.open(u());g.focus(N),e.hasLoaded()||(g.block("Loading emojis..."),e.waitForLoad().then((()=>{g.redial(u()),s.throttle(g),g.focus(N),g.unblock()})).catch((t=>{g.redial({title:"Emojis",body:{type:"panel",items:[{type:"alertbanner",level:"error",icon:"warning",text:"Could not load emojis"}]},buttons:[{type:"cancel",text:"Close",primary:!0}],initialData:{pattern:"",results:[]}}),g.focus(N),g.unblock()})))},D=t=>e=>{const o=()=>{e.setEnabled(t.selection.isEditable())};return t.on("NodeChange",o),o(),()=>{t.off("NodeChange",o)}};t.add("emoticons",((t,e)=>{((t,e)=>{const o=t.options.register;o("emoticons_database",{processor:"string",default:"emojis"}),o("emoticons_database_url",{processor:"string",default:`${e}/js/${w(t)}${t.suffix}.js`}),o("emoticons_database_id",{processor:"string",default:"tinymce.plugins.emoticons"}),o("emoticons_append",{processor:"object",default:{}}),o("emoticons_images_url",{processor:"string",default:"https://cdnjs.cloudflare.com/ajax/libs/twemoji/15.1.0/72x72/"})})(t,e);const o=((t,e,o)=>{const n=y(),r=y(),s=A(t),a=t=>{return o="<img",(e=t.char).length>=4&&e.substr(0,4)===o?t.char.replace(/src="([^"]+)"/,((t,e)=>`src="${s}${e}"`)):t.char;var e,o};t.on("init",(()=>{v.load(o,e).then((e=>{const o=E(t);(t=>{const e={},o=[];g(t,((t,n)=>{const r={title:n,keywords:t.keywords,char:a(t),category:x(O,t.category)},s=void 0!==e[r.category]?e[r.category]:[];e[r.category]=s.concat([r]),o.push(r)})),n.set(e),r.set(o)})(h(e,o))}),(t=>{console.log(`Failed to load emojis: ${t}`),n.set({}),r.set([])}))}));const l=()=>r.get().getOr([]),u=()=>n.isSet()&&r.isSet();return{listCategories:()=>[k].concat(c(n.get().getOr({}))),hasLoaded:u,waitForLoad:()=>u()?Promise.resolve(!0):new Promise(((t,o)=>{let n=15;const r=setInterval((()=>{u()?(clearInterval(r),t(!0)):(n--,n<0&&(console.log("Could not load emojis from url: "+e),clearInterval(r),o(!1)))}),100)})),listAll:l,listCategory:t=>t===k?l():n.get().bind((e=>i.from(e[t]))).getOr([])}})(t,j(t),C(t));return((t,e)=>{t.addCommand("mceEmoticons",(()=>T(t,e)))})(t,o),(t=>{const e=()=>t.execCommand("mceEmoticons");t.ui.registry.addButton("emoticons",{tooltip:"Emojis",icon:"emoji",onAction:e,onSetup:D(t)}),t.ui.registry.addMenuItem("emoticons",{text:"Emojis...",icon:"emoji",onAction:e,onSetup:D(t)})})(t),((t,e)=>{t.ui.registry.addAutocompleter("emoticons",{trigger:":",columns:"auto",minChars:2,fetch:(t,o)=>e.waitForLoad().then((()=>{const n=e.listAll();return S(n,t,i.some(o))})),onAction:(e,o,n)=>{t.selection.setRng(o),t.insertContent(n),e.hide()}})})(t,o),(t=>{t.on("PreInit",(()=>{t.parser.addAttributeFilter("data-emoticon",(t=>{(t=>{for(let o=0,n=t.length;o<n;o++)(e=t[o]).attr("data-mce-resize","false"),e.attr("data-mce-placeholder","1");var e})(t)}))}))})(t),{getAllEmojis:()=>o.waitForLoad().then((()=>o.listAll()))}}))}();