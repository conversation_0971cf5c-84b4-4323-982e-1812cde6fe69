!function(){"use strict";const e=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=r=e,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":t;var n,r,o,s})(t)===e,t=e=>t=>typeof t===e,n=e("string"),r=e("array"),o=t("boolean"),s=t("number"),a=()=>{},l=e=>()=>e,i=l(!0);class c{constructor(e,t){this.tag=e,this.value=t}static some(e){return new c(!0,e)}static none(){return c.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?c.some(e(this.value)):c.none()}bind(e){return this.tag?e(this.value):c.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:c.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return null==e?c.none():c.some(e)}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}c.singletonNone=new c(!1);const d=Array.prototype.slice,u=Array.prototype.push,m=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},h=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},g=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},f=(e,t)=>(e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!r(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);u.apply(t,e[n])}return t})(m(e,t)),p=Object.hasOwnProperty,x=(e,t)=>p.call(e,t),y=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}};var b=tinymce.util.Tools.resolve("tinymce.PluginManager");const v=l("[~\u2116|!-*+-\\/:;?@\\[-`{}\xa1\xab\xb7\xbb\xbf;\xb7\u055a-\u055f\u0589\u058a\u05be\u05c0\u05c3\u05c6\u05f3\u05f4\u0609\u060a\u060c\u060d\u061b\u061e\u061f\u066a-\u066d\u06d4\u0700-\u070d\u07f7-\u07f9\u0830-\u083e\u085e\u0964\u0965\u0970\u0df4\u0e4f\u0e5a\u0e5b\u0f04-\u0f12\u0f3a-\u0f3d\u0f85\u0fd0-\u0fd4\u0fd9\u0fda\u104a-\u104f\u10fb\u1361-\u1368\u1400\u166d\u166e\u169b\u169c\u16eb-\u16ed\u1735\u1736\u17d4-\u17d6\u17d8-\u17da\u1800-\u180a\u1944\u1945\u1a1e\u1a1f\u1aa0-\u1aa6\u1aa8-\u1aad\u1b5a-\u1b60\u1bfc-\u1bff\u1c3b-\u1c3f\u1c7e\u1c7f\u1cd3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205e\u207d\u207e\u208d\u208e\u3008\u3009\u2768-\u2775\u27c5\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc\u29fd\u2cf9-\u2cfc\u2cfe\u2cff\u2d70\u2e00-\u2e2e\u2e30\u2e31\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30a0\u30fb\ua4fe\ua4ff\ua60d-\ua60f\ua673\ua67e\ua6f2-\ua6f7\ua874-\ua877\ua8ce\ua8cf\ua8f8-\ua8fa\ua92e\ua92f\ua95f\ua9c1-\ua9cd\ua9de\ua9df\uaa5c-\uaa5f\uaade\uaadf\uabeb\ufd3e\ufd3f\ufe10-\ufe19\ufe30-\ufe52\ufe54-\ufe61\ufe63\ufe68\ufe6a\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a\uff1b\uff1f\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]");var w=tinymce.util.Tools.resolve("tinymce.Env"),C=tinymce.util.Tools.resolve("tinymce.util.Tools");const E=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},O=E,N=e=>3===(e=>e.dom.nodeType)(e);const T=(e,t)=>({element:e,offset:t}),k=(e,t)=>{((e,t)=>{const n=(e=>c.from(e.dom.parentNode).map(O))(e);n.each((n=>{n.dom.insertBefore(t.dom,e.dom)}))})(e,t),((e,t)=>{e.dom.appendChild(t.dom)})(t,e)},A=(e=>{const t=t=>e(t)?c.from(t.dom.nodeValue):c.none();return{get:n=>{if(!e(n))throw new Error("Can only get text value of a text node");return t(n).getOr("")},getOption:t,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(N),S=e=>A.get(e);var B=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker");const F=(e,t)=>e.isBlock(t)||x(e.schema.getVoidElements(),t.nodeName),I=(e,t)=>!e.isEditable(t),M=(e,t)=>!e.isBlock(t)&&x(e.schema.getWhitespaceElements(),t.nodeName),R=(e,t)=>((e,t)=>{const n=(e=>m(e.dom.childNodes,O))(e);return n.length>0&&t<n.length?T(n[t],0):T(e,t)})(O(e),t),D=(e,t,n,r,o,s=!0)=>{let a=s?t(!1):n;for(;a;){const n=I(e,a);if(n||M(e,a)){if(n?r.cef(a):r.boundary(a))break;a=t(!0)}else{if(F(e,a)){if(r.boundary(a))break}else 3===a.nodeType&&r.text(a);if(a===o)break;a=t(!1)}}},P=(e,t,n,r,o)=>{var s;if(((e,t)=>F(e,t)||I(e,t)||M(e,t)||((e,t)=>"true"===e.getContentEditable(t)&&t.parentNode&&!e.isEditable(t.parentNode))(e,t))(e,n))return;const a=null!==(s=e.getParent(r,e.isBlock))&&void 0!==s?s:e.getRoot(),l=new B(n,a),c=o?l.next.bind(l):l.prev.bind(l);D(e,c,n,{boundary:i,cef:i,text:e=>{o?t.fOffset+=e.length:t.sOffset+=e.length,t.elements.push(O(e))}})},W=(e,t,n,r,o,s=!0)=>{const a=new B(n,t),l=[];let i={sOffset:0,fOffset:0,elements:[]};P(e,i,n,t,!1);const c=()=>(i.elements.length>0&&(l.push(i),i={sOffset:0,fOffset:0,elements:[]}),!1);return D(e,a.next.bind(a),n,{boundary:c,cef:e=>(c(),o&&l.push(...o.cef(e)),!1),text:e=>{i.elements.push(O(e)),o&&o.text(e,i)}},r,s),r&&P(e,i,r,t,!0),c(),l},$=(e,t)=>{const n=R(t.startContainer,t.startOffset),r=n.element.dom,o=R(t.endContainer,t.endOffset),s=o.element.dom;return W(e,t.commonAncestorContainer,r,s,{text:(e,t)=>{e===s?t.fOffset+=e.length-o.offset:e===r&&(t.sOffset+=n.offset)},cef:t=>{return(e=>{const t=d.call(e,0);return t.sort(((e,t)=>((e,t)=>((e,t,n)=>!!(e.compareDocumentPosition(t)&n))(e,t,Node.DOCUMENT_POSITION_PRECEDING))(e.elements[0].dom,t.elements[0].dom)?1:-1)),t})(f((n=O(t),((e,t)=>{const n=void 0===t?document:t.dom;return 1!==(r=n).nodeType&&9!==r.nodeType&&11!==r.nodeType||0===r.childElementCount?[]:m(n.querySelectorAll(e),O);var r})("*[contenteditable=true]",n)),(t=>{const n=t.dom;return W(e,n,n)})));var n}},!1)},V=(e,t)=>t.collapsed?[]:$(e,t),j=(e,t)=>{const n=e.createRng();return n.selectNode(t),V(e,n)},z=(e,t)=>f(t,(t=>{const n=t.elements,r=m(n,S).join(""),o=((e,t,n=0,r=e.length)=>{const o=t.regex;o.lastIndex=n;const s=[];let a;for(;a=o.exec(e);){const e=a[t.matchIndex],n=a.index+a[0].indexOf(e),l=n+e.length;if(l>r)break;s.push({start:n,finish:l}),o.lastIndex=l}return s})(r,e,t.sOffset,r.length-t.fOffset);return((e,t)=>{const n=(r=e,o=(e,n)=>{const r=S(n),o=e.last,s=o+r.length,a=f(t,((e,t)=>e.start<s&&e.finish>o?[{element:n,start:Math.max(o,e.start)-o,finish:Math.min(s,e.finish)-o,matchId:t}]:[]));return{results:e.results.concat(a),last:s}},s={results:[],last:0},h(r,((e,t)=>{s=o(s,e)})),s).results;var r,o,s;return((e,t)=>{if(0===e.length)return[];{let n=t(e[0]);const r=[];let o=[];for(let s=0,a=e.length;s<a;s++){const a=e[s],l=t(a);l!==n&&(r.push(o),o=[]),n=l,o.push(a)}return 0!==o.length&&r.push(o),r}})(n,(e=>e.matchId))})(n,o)})),U=(e,t)=>{g(e,((e,r)=>{g(e,(e=>{const a=O(t.cloneNode(!1));((e,t,r)=>{((e,t,r)=>{if(!(n(r)||o(r)||s(r)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",r,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,r+"")})(e.dom,t,r)})(a,"data-mce-index",r);const l=e.element.dom;if(l.length===e.finish&&0===e.start)k(e.element,a);else{l.length!==e.finish&&l.splitText(e.finish);const t=l.splitText(e.start);k(O(t),a)}}))}))},_=e=>e.getAttribute("data-mce-index"),q=(e,t,n,r)=>{const o=e.dom.create("span",{"data-mce-bogus":1});o.className="mce-match-marker";const s=e.getBody();return ee(e,t,!1),r?((e,t,n,r)=>{const o=n.getBookmark(),s=e.select("td[data-mce-selected],th[data-mce-selected]"),a=s.length>0?((e,t)=>f(t,(t=>j(e,t))))(e,s):V(e,n.getRng()),l=z(t,a);return U(l,r),n.moveToBookmark(o),l.length})(e.dom,n,e.selection,o):((e,t,n,r)=>{const o=j(e,n),s=z(t,o);return U(s,r),s.length})(e.dom,n,s,o)},G=e=>{var t;const n=e.parentNode;e.firstChild&&n.insertBefore(e.firstChild,e),null===(t=e.parentNode)||void 0===t||t.removeChild(e)},K=(e,t)=>{const n=[],r=C.toArray(e.getBody().getElementsByTagName("span"));if(r.length)for(let e=0;e<r.length;e++){const o=_(r[e]);null!==o&&o.length&&o===t.toString()&&n.push(r[e])}return n},H=(e,t,n)=>{const r=t.get();let o=r.index;const s=e.dom;n?o+1===r.count?o=0:o++:o-1==-1?o=r.count-1:o--,s.removeClass(K(e,r.index),"mce-match-marker-selected");const a=K(e,o);return a.length?(s.addClass(K(e,o),"mce-match-marker-selected"),e.selection.scrollIntoView(a[0]),o):-1},J=(e,t)=>{const n=t.parentNode;e.remove(t),n&&e.isEmpty(n)&&e.remove(n)},L=(e,t,n,r,o,s)=>{const a=e.selection,l=((e,t)=>{const n="("+e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&").replace(/\s/g,"[^\\S\\r\\n\\uFEFF]")+")";return t?`(?:^|\\s|${v()})`+n+`(?=$|\\s|${v()})`:n})(n,o),i=a.isForward(),c={regex:new RegExp(l,r?"g":"gi"),matchIndex:1},d=q(e,t,c,s);if(w.browser.isSafari()&&a.setRng(a.getRng(),i),d){const a=H(e,t,!0);t.set({index:a,count:d,text:n,matchCase:r,wholeWord:o,inSelection:s})}return d},Q=(e,t)=>{const n=H(e,t,!0);t.set({...t.get(),index:n})},X=(e,t)=>{const n=H(e,t,!1);t.set({...t.get(),index:n})},Y=e=>{const t=_(e);return null!==t&&t.length>0},Z=(e,t,n,r,o)=>{const s=t.get(),a=s.index;let l,i=a;r=!1!==r;const c=e.getBody(),d=C.grep(C.toArray(c.getElementsByTagName("span")),Y);for(let t=0;t<d.length;t++){const c=_(d[t]);let u=l=parseInt(c,10);if(o||u===s.index){for(n.length?(d[t].innerText=n,G(d[t])):J(e.dom,d[t]);d[++t];){if(u=parseInt(_(d[t]),10),u!==l){t--;break}J(e.dom,d[t])}r&&i--}else l>a&&d[t].setAttribute("data-mce-index",String(l-1))}return t.set({...s,count:o?0:s.count-1,index:i}),r?Q(e,t):X(e,t),!o&&t.get().count>0},ee=(e,t,n)=>{let r,o;const s=t.get(),a=C.toArray(e.getBody().getElementsByTagName("span"));for(let e=0;e<a.length;e++){const t=_(a[e]);null!==t&&t.length&&(t===s.index.toString()&&(r||(r=a[e].firstChild),o=a[e].firstChild),G(a[e]))}if(t.set({...s,index:-1,count:0,text:""}),r&&o){const t=e.dom.createRng();return t.setStart(r,0),t.setEnd(o,o.data.length),!1!==n&&e.selection.setRng(t),t}},te=(e,t)=>{const n=(()=>{const e=(e=>{const t=y(c.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(c.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(c.some(e))}}})(a);return{...e,on:t=>e.get().each(t)}})();e.undoManager.add();const r=C.trim(e.selection.getContent({format:"text"})),o=e=>{e.setEnabled("next",((e,t)=>t.get().count>1)(0,t)),e.setEnabled("prev",((e,t)=>t.get().count>1)(0,t))},s=(e,t)=>{h(["replace","replaceall","prev","next"],(n=>e.setEnabled(n,!t)))},l=(e,t)=>{t.redial(p(e,t.getData()))},i=(e,t)=>{w.browser.isSafari()&&w.deviceType.isTouch()&&("find"===t||"replace"===t||"replaceall"===t)&&e.focus(t)},d=n=>{ee(e,t,!1),s(n,!0),o(n)},u=n=>{const r=n.getData(),a=t.get();if(r.findtext.length){if(a.text===r.findtext&&a.matchCase===r.matchcase&&a.wholeWord===r.wholewords)Q(e,t);else{const o=L(e,t,r.findtext,r.matchcase,r.wholewords,r.inselection);o<=0&&l(!0,n),s(n,0===o)}o(n)}else d(n)},m=t.get(),g={findtext:r,replacetext:"",wholewords:m.wholeWord,matchcase:m.matchCase,inselection:m.inSelection},f=e=>{const t=[{type:"label",label:"Find",for:"findtext",items:[{type:"bar",items:[{type:"input",name:"findtext",maximized:!0,inputMode:"search"},{type:"button",name:"prev",text:"Previous",icon:"action-prev",enabled:!1,borderless:!0},{type:"button",name:"next",text:"Next",icon:"action-next",enabled:!1,borderless:!0}]}]},{type:"input",name:"replacetext",label:"Replace with",inputMode:"search"}];return e&&t.push({type:"alertbanner",level:"error",text:"Could not find the specified string.",icon:"warning"}),t},p=(n,r)=>({title:"Find and Replace",size:"normal",body:{type:"panel",items:f(n)},buttons:[{type:"menu",name:"options",icon:"preferences",tooltip:"Preferences",align:"start",items:[{type:"togglemenuitem",name:"matchcase",text:"Match case"},{type:"togglemenuitem",name:"wholewords",text:"Find whole words only"},{type:"togglemenuitem",name:"inselection",text:"Find in selection"}]},{type:"custom",name:"find",text:"Find",primary:!0},{type:"custom",name:"replace",text:"Replace",enabled:!1},{type:"custom",name:"replaceall",text:"Replace all",enabled:!1}],initialData:r,onChange:(e,r)=>{n&&l(!1,e),"findtext"===r.name&&t.get().count>0&&d(e)},onAction:(n,r)=>{const s=n.getData();switch(r.name){case"find":u(n);break;case"replace":Z(e,t,s.replacetext)?o(n):d(n);break;case"replaceall":Z(e,t,s.replacetext,!0,!0),d(n);break;case"prev":X(e,t),o(n);break;case"next":Q(e,t),o(n);break;case"matchcase":case"wholewords":case"inselection":l(!1,n),(e=>{const n=e.getData(),r=t.get();t.set({...r,matchCase:n.matchcase,wholeWord:n.wholewords,inSelection:n.inselection})})(n),d(n)}i(n,r.name)},onSubmit:e=>{u(e),i(e,"find")},onClose:()=>{e.focus(),ee(e,t),e.undoManager.add()}});n.set(e.windowManager.open(p(!1,g),{inline:"toolbar"}))},ne=(e,t)=>()=>{te(e,t)};b.add("searchreplace",(e=>{const t=y({index:-1,count:0,text:"",matchCase:!1,wholeWord:!1,inSelection:!1});return((e,t)=>{e.addCommand("SearchReplace",(()=>{te(e,t)}))})(e,t),((e,t)=>{e.ui.registry.addMenuItem("searchreplace",{text:"Find and replace...",shortcut:"Meta+F",onAction:ne(e,t),icon:"search"}),e.ui.registry.addButton("searchreplace",{tooltip:"Find and replace",onAction:ne(e,t),icon:"search",shortcut:"Meta+F"}),e.shortcuts.add("Meta+F","",ne(e,t))})(e,t),((e,t)=>({done:n=>ee(e,t,n),find:(n,r,o,s=!1)=>L(e,t,n,r,o,s),next:()=>Q(e,t),prev:()=>X(e,t),replace:(n,r,o)=>Z(e,t,n,r,o)}))(e,t)}))}();