{"name": "gemini-blog-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:analyze": "ANALYZE=true vite build", "build:check-size": "npm run build && node scripts/check-bundle-size.js", "preview": "vite preview", "analyze": "npm run build:analyze", "perf:test": "npm run build && npm run preview & sleep 5 && lhci autorun && pkill -f 'vite preview'", "perf:ci": "lhci autorun", "perf:report": "node scripts/performance-report.js"}, "dependencies": {"@google/genai": "^1.11.0", "@hookform/resolvers": "^5.2.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/vite": "^4.1.11", "@tinymce/tinymce-react": "^6.2.1", "@types/prismjs": "^1.26.5", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "highlight.js": "^11.11.1", "lucide-react": "^0.533.0", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "react-is": "^19.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tinymce": "^7.9.1", "zod": "^4.0.11"}, "devDependencies": {"@lhci/cli": "^0.15.1", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "lighthouse": "^12.8.0", "rollup-plugin-visualizer": "^6.0.3", "terser": "^5.43.1", "tw-animate-css": "^1.3.6", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-bundle-analyzer": "^1.1.0"}}